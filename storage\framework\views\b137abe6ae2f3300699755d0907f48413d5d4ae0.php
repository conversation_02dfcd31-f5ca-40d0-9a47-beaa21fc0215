<?php $__env->startSection('title', $activity->title . ' - ผลงาน - ' . ($settings['site_name'] ?? 'บริการจัดงานศพ')); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section with Cover Image -->
<?php
    $heroCoverImage = $activity->images->where('is_cover', true)->first() ?? $activity->images->first();
    $heroCoverImagePath = $heroCoverImage ? $heroCoverImage->image_path : $activity->image;
?>
<section class="hero-section" style="background-image: linear-gradient(rgba(44, 62, 80, 0.7), rgba(44, 62, 80, 0.7)), url('<?php echo e(asset('storage/' . $heroCoverImagePath)); ?>'); background-size: cover; background-position: center;">
    <div class="hero-content-overlay">
        <div class="container">
            <div class="text-center">
                <nav aria-label="breadcrumb" class="mb-4">
                    <ol class="breadcrumb justify-content-center bg-transparent">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('home')); ?>" class="text-white-50">หน้าหลัก</a></li>
                        <li class="breadcrumb-item"><a href="<?php echo e(route('activities')); ?>" class="text-white-50">ผลงาน</a></li>
                        <li class="breadcrumb-item active text-white" aria-current="page"><?php echo e(Str::limit($activity->title, 30)); ?></li>
                    </ol>
                </nav>
                <h1 class="display-4 fw-bold mb-4 text-white"><?php echo e($activity->title); ?></h1>
                <p class="lead text-white"><?php echo e($activity->description); ?></p>
                <div class="d-flex justify-content-center gap-4 mt-4">
                    <div class="text-white-50">
                        <i class="fas fa-calendar me-2"></i>
                        <?php echo e($activity->activity_date->format('d/m/Y')); ?>

                    </div>
                    <?php if($activity->location): ?>
                    <div class="text-white-50">
                        <i class="fas fa-map-marker-alt me-2"></i>
                        <?php echo e($activity->location); ?>

                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Activity Detail Section -->
<section class="py-5">
    <div class="container">
        <!-- Back Button -->
        <div class="mb-4">
            <button onclick="goBackTo('<?php echo e(route('activities')); ?>')" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>ย้อนกลับ
            </button>
        </div>

        <div class="row">
            <!-- Main Content -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-body p-4">


                        <!-- Main Image -->
                        <div class="mb-4">
                            <?php
                                $mainImage = $activity->images->where('is_cover', true)->first() ?? $activity->images->first();
                                $mainImagePath = $mainImage ? $mainImage->image_path : $activity->image;
                            ?>
                            <div class="img-container-fixed img-size-xlarge rounded shadow-sm">
                                <img src="<?php echo e(asset('storage/' . $mainImagePath)); ?>"
                                     class="img-fit-contain main-image"
                                     alt="<?php echo e($activity->title); ?>"
                                     style="cursor: pointer;"
                                     onclick="openImageModal(0)"
                                     id="mainImage">
                            </div>
                        </div>

                        <!-- Image Gallery -->
                        <?php if($activity->images->count() > 0): ?>
                        <div class="mb-4">
                            <h5 class="mb-3">แกลเลอรี่รูปภาพ</h5>
                            <div class="row g-2">
                                <?php $__currentLoopData = $activity->images->sortBy('sort_order'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="col-md-3 col-4">
                                    <div class="gallery-image-container img-size-thumbnail">
                                        <img src="<?php echo e(asset('storage/' . $image->image_path)); ?>"
                                             class="img-fit-contain gallery-thumbnail"
                                             alt="<?php echo e($image->caption ?? $activity->title); ?>"
                                             style="cursor: pointer;"
                                             onclick="openImageModal(<?php echo e($loop->index); ?>)"
                                             data-image="<?php echo e(asset('storage/' . $image->image_path)); ?>"
                                             data-caption="<?php echo e($image->caption ?? $activity->title); ?>">
                                        <?php if($image->is_cover): ?>
                                        <div class="position-absolute top-0 end-0 m-1">
                                            <span class="badge bg-primary">รูปหลัก</span>
                                        </div>
                                        <?php endif; ?>
                                        <div class="position-absolute bottom-0 start-0 w-100 bg-dark bg-opacity-75 text-white p-1 gallery-thumb-caption" style="font-size: 0.75rem;">
                                            <?php echo e(Str::limit($image->caption ?? 'รูปที่ ' . $loop->iteration, 20)); ?>

                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                        </div>
                        <?php endif; ?>

                        <!-- Content -->
                        <div class="content">
                            <h3 class="mb-3">รายละเอียด</h3>
                            <?php if($activity->details): ?>
                                <div class="mb-4">
                                    <?php echo nl2br(e($activity->details)); ?>

                                </div>
                            <?php else: ?>
                                <p class="text-muted mb-4"><?php echo e($activity->description); ?></p>
                            <?php endif; ?>

                            <!-- Activity Info -->
                            <div class="row g-3 mb-4">
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center p-3 bg-light rounded">
                                        <i class="fas fa-calendar text-primary me-3 fa-lg"></i>
                                        <div>
                                            <small class="text-muted d-block">วันที่</small>
                                            <strong><?php echo e($activity->activity_date->format('d/m/Y')); ?></strong>
                                        </div>
                                    </div>
                                </div>
                                <?php if($activity->location): ?>
                                <div class="col-md-6">
                                    <div class="d-flex align-items-center p-3 bg-light rounded">
                                        <i class="fas fa-map-marker-alt text-primary me-3 fa-lg"></i>
                                        <div>
                                            <small class="text-muted d-block">สถานที่</small>
                                            <strong><?php echo e($activity->location); ?></strong>
                                        </div>
                                    </div>
                                </div>
                                <?php endif; ?>
                            </div>

                            <!-- Share Section -->
                            <div class="border-top pt-4">
                                <h5 class="mb-3">แชร์ผลงานนี้</h5>
                                <div class="d-flex gap-2">
                                    <a href="https://www.facebook.com/sharer/sharer.php?u=<?php echo e(urlencode(request()->fullUrl())); ?>" 
                                       target="_blank" class="btn btn-outline-primary btn-sm">
                                        <i class="fab fa-facebook me-1"></i>Facebook
                                    </a>
                                    <a href="https://line.me/R/msg/text/?<?php echo e(urlencode($activity->title . ' - ' . request()->fullUrl())); ?>" 
                                       target="_blank" class="btn btn-outline-success btn-sm">
                                        <i class="fab fa-line me-1"></i>Line
                                    </a>
                                    <button class="btn btn-outline-secondary btn-sm" onclick="copyToClipboard()">
                                        <i class="fas fa-copy me-1"></i>คัดลอกลิงก์
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-lg-4">
                <!-- Contact Card -->
                <div class="card mb-4">
                    <div class="card-body text-center">
                        <h5 class="card-title">ต้องการความช่วยเหลือ?</h5>
                        <p class="card-text">เราพร้อมให้คำปรึกษาและดูแลท่านในช่วงเวลาที่ยากลำบาก</p>
                        <div class="d-grid gap-2">
                            <a href="<?php echo e(route('contact')); ?>" class="btn btn-primary">
                                <i class="fas fa-envelope me-2"></i>ติดต่อเรา
                            </a>
                            <a href="tel:<?php echo e($settings['contact_phone'] ?? ''); ?>" class="btn btn-outline-primary">
                                <i class="fas fa-phone me-2"></i><?php echo e($settings['contact_phone'] ?? '02-xxx-xxxx'); ?>

                            </a>
                        </div>
                        <small class="text-muted d-block mt-2">
                            <i class="fas fa-clock me-1"></i>
                            บริการตลอด 24 ชั่วโมง
                        </small>
                    </div>
                </div>

                <!-- Related Activities -->
                <?php if($relatedActivities->count() > 0): ?>
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">ผลงานอื่นๆ</h5>
                    </div>
                    <div class="card-body p-0">
                        <?php $__currentLoopData = $relatedActivities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $related): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="d-flex p-3 border-bottom">
                            <?php
                                $relatedCoverImage = $related->images->where('is_cover', true)->first() ?? $related->images->first();
                                $relatedCoverImagePath = $relatedCoverImage ? $relatedCoverImage->image_path : $related->image;
                            ?>
                            <img src="<?php echo e(asset('storage/' . $relatedCoverImagePath)); ?>"
                                 class="rounded me-3"
                                 style="width: 60px; height: 60px; object-fit: cover;"
                                 alt="<?php echo e($related->title); ?>">
                            <div class="flex-grow-1">
                                <h6 class="mb-1">
                                    <a href="<?php echo e(route('activities.show', $related->id)); ?>" 
                                       class="text-decoration-none">
                                        <?php echo e(Str::limit($related->title, 40)); ?>

                                    </a>
                                </h6>
                                <small class="text-muted">
                                    <i class="fas fa-calendar me-1"></i>
                                    <?php echo e($related->activity_date->format('d/m/Y')); ?>

                                </small>
                            </div>
                        </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</section>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1" aria-hidden="true" data-bs-backdrop="true" data-bs-keyboard="true">
    <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><?php echo e($activity->title); ?></h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" id="modalCloseBtn"></button>
            </div>
            <div class="modal-body text-center p-0 position-relative">
                <?php
                    $modalMainImage = $activity->images->where('is_cover', true)->first() ?? $activity->images->first();
                    $modalMainImagePath = $modalMainImage ? $modalMainImage->image_path : $activity->image;
                ?>
                <img src="<?php echo e(asset('storage/' . $modalMainImagePath)); ?>"
                     class="img-fluid w-100"
                     alt="<?php echo e($activity->title); ?>"
                     id="modalImage"
                     style="max-height: 80vh; object-fit: contain;">

                <!-- Navigation arrows -->
                <?php if($activity->images->count() > 1): ?>
                <button class="btn btn-dark btn-sm position-absolute top-50 start-0 translate-middle-y ms-3"
                        id="prevImageBtn" style="z-index: 10;">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <button class="btn btn-dark btn-sm position-absolute top-50 end-0 translate-middle-y me-3"
                        id="nextImageBtn" style="z-index: 10;">
                    <i class="fas fa-chevron-right"></i>
                </button>
                <?php endif; ?>
            </div>
            <?php if($activity->images->count() > 1): ?>
            <div class="modal-footer justify-content-center">
                <div class="d-flex gap-2 flex-wrap" id="modalThumbnails">
                    <?php $__currentLoopData = $activity->images->sortBy('sort_order'); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <div class="modal-thumb <?php echo e($index === 0 ? 'active' : ''); ?>"
                         style="width: 60px; height: 60px; cursor: pointer; border: 2px solid transparent; border-radius: 4px; overflow: hidden;"
                         data-index="<?php echo e($index); ?>"
                         data-image="<?php echo e(asset('storage/' . $image->image_path)); ?>"
                         data-alt="<?php echo e($image->caption ?? $activity->title); ?>">
                        <img src="<?php echo e(asset('storage/' . $image->image_path)); ?>"
                             class="img-thumbnail w-100 h-100"
                             style="object-fit: cover;"
                             alt="<?php echo e($image->caption ?? $activity->title); ?>">
                    </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<!-- Stable Modal Gallery Script -->
<script src="<?php echo e(asset('js/stable-modal.js')); ?>"></script>
<script>
// Activity Image Gallery Management System (same as Service)
class ActivityImageGallery {
    constructor() {
        this.currentImageIndex = 0;
        this.images = [];
        this.init();
    }

    init() {
        this.loadImages();
        this.setupEventListeners();
        this.setInitialActiveStates();
    }

    loadImages() {
        // Load all gallery images
        const galleryThumbnails = document.querySelectorAll('.gallery-thumbnail');
        galleryThumbnails.forEach((thumb, index) => {
            this.images.push({
                src: thumb.dataset.image,
                caption: thumb.dataset.caption || thumb.alt,
                element: thumb
            });
        });
    }

    setupEventListeners() {
        // Main image click to open modal
        const mainImage = document.getElementById('mainImage');
        if (mainImage) {
            mainImage.addEventListener('click', () => {
                this.openModal();
            });
        }

        // Gallery thumbnail clicks
        document.querySelectorAll('.gallery-thumbnail').forEach((thumb, index) => {
            thumb.addEventListener('click', (e) => {
                e.preventDefault();
                this.changeMainImage(index);
            });
        });

        // Modal thumbnail clicks
        document.querySelectorAll('.modal-thumb').forEach((thumb, index) => {
            thumb.addEventListener('click', (e) => {
                e.preventDefault();
                this.changeModalImage(index);
            });
        });

        // Keyboard navigation in modal
        document.addEventListener('keydown', (e) => {
            const modal = document.getElementById('imageModal');
            if (modal && modal.classList.contains('show')) {
                if (e.key === 'ArrowLeft') {
                    this.previousImage();
                } else if (e.key === 'ArrowRight') {
                    this.nextImage();
                } else if (e.key === 'Escape') {
                    this.closeModal();
                }
            }
        });
    }

    setInitialActiveStates() {
        // Set first thumbnail as active
        const firstThumbnail = document.querySelector('.gallery-thumbnail');
        if (firstThumbnail) {
            firstThumbnail.parentElement.classList.add('active-thumbnail');
        }

        // Set first modal thumbnail as active
        const firstModalThumb = document.querySelector('.modal-thumb');
        if (firstModalThumb) {
            this.setActiveModalThumb(firstModalThumb);
        }
    }

    changeMainImage(index) {
        if (index < 0 || index >= this.images.length) return;

        this.currentImageIndex = index;
        const image = this.images[index];

        // Update main image with loading state
        const mainImage = document.getElementById('mainImage');
        if (mainImage) {
            mainImage.style.opacity = '0.5';

            // Preload image
            const newImg = new Image();
            newImg.onload = () => {
                mainImage.src = image.src;
                mainImage.alt = image.caption;
                mainImage.style.opacity = '1';
            };
            newImg.onerror = () => {
                console.error('Failed to load image:', image.src);
                mainImage.style.opacity = '1';
                this.showError('ไม่สามารถโหลดรูปภาพได้');
            };
            newImg.src = image.src;
        }

        // Update modal image
        this.updateModalImage(image);

        // Update active states
        this.updateActiveThumbnails(index);

        // Smooth scroll to main image
        if (mainImage) {
            mainImage.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    }

    changeModalImage(index) {
        if (index < 0 || index >= this.images.length) return;

        this.currentImageIndex = index;
        const image = this.images[index];

        this.updateModalImage(image);
        this.updateActiveModalThumbs(index);
    }

    updateModalImage(image) {
        const modalImage = document.getElementById('modalImage');
        if (modalImage) {
            modalImage.style.opacity = '0.5';

            const newImg = new Image();
            newImg.onload = () => {
                modalImage.src = image.src;
                modalImage.alt = image.caption;
                modalImage.style.opacity = '1';
            };
            newImg.onerror = () => {
                console.error('Failed to load modal image:', image.src);
                modalImage.style.opacity = '1';
            };
            newImg.src = image.src;
        }
    }

    updateActiveThumbnails(activeIndex) {
        // Remove active class from all thumbnails
        document.querySelectorAll('.gallery-thumbnail').forEach((thumb, index) => {
            const container = thumb.parentElement;
            if (index === activeIndex) {
                container.classList.add('active-thumbnail');
            } else {
                container.classList.remove('active-thumbnail');
            }
        });
    }

    updateActiveModalThumbs(activeIndex) {
        // Remove active class from all modal thumbnails
        document.querySelectorAll('.modal-thumb').forEach((thumb, index) => {
            if (index === activeIndex) {
                this.setActiveModalThumb(thumb);
            } else {
                this.removeActiveModalThumb(thumb);
            }
        });
    }

    setActiveModalThumb(thumb) {
        thumb.classList.add('border-primary');
        thumb.style.borderWidth = '3px';
        thumb.style.transform = 'scale(1.1)';
    }

    removeActiveModalThumb(thumb) {
        thumb.classList.remove('border-primary');
        thumb.style.borderWidth = '1px';
        thumb.style.transform = 'scale(1)';
    }

    openModal() {
        const modalElement = document.getElementById('imageModal');
        if (modalElement) {
            // Clean up any existing modal instances
            const existingModal = bootstrap.Modal.getInstance(modalElement);
            if (existingModal) {
                existingModal.dispose();
            }

            // Create new modal instance
            const modal = new bootstrap.Modal(modalElement, {
                backdrop: true,
                keyboard: true,
                focus: true
            });

            modal.show();

            // Add event listeners for proper cleanup
            modalElement.addEventListener('hidden.bs.modal', this.handleModalHidden.bind(this), { once: true });
        }
    }

    closeModal() {
        const modalElement = document.getElementById('imageModal');
        const modal = bootstrap.Modal.getInstance(modalElement);
        if (modal) {
            modal.hide();
        } else {
            // Force close if instance not found
            modalElement.classList.remove('show');
            modalElement.style.display = 'none';
            document.body.classList.remove('modal-open');

            // Remove backdrop if exists
            const backdrop = document.querySelector('.modal-backdrop');
            if (backdrop) {
                backdrop.remove();
            }
        }
    }

    handleModalHidden() {
        // Clean up modal instance
        const modalElement = document.getElementById('imageModal');
        const modal = bootstrap.Modal.getInstance(modalElement);
        if (modal) {
            modal.dispose();
        }

        // Force cleanup of backdrop and body classes
        document.body.classList.remove('modal-open');
        document.body.style.removeProperty('padding-right');

        const backdrops = document.querySelectorAll('.modal-backdrop');
        backdrops.forEach(backdrop => backdrop.remove());
    }

    nextImage() {
        const nextIndex = (this.currentImageIndex + 1) % this.images.length;
        this.changeModalImage(nextIndex);
    }

    previousImage() {
        const prevIndex = (this.currentImageIndex - 1 + this.images.length) % this.images.length;
        this.changeModalImage(prevIndex);
    }

    showError(message) {
        // Create a simple toast notification
        const toast = document.createElement('div');
        toast.className = 'toast-notification';
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #dc3545;
            color: white;
            padding: 12px 20px;
            border-radius: 5px;
            z-index: 9999;
            animation: slideIn 0.3s ease;
        `;
        toast.textContent = message;
        document.body.appendChild(toast);

        setTimeout(() => {
            toast.remove();
        }, 3000);
    }
}

// Copy to clipboard function
function copyToClipboard(event) {
    event.preventDefault();

    navigator.clipboard.writeText(window.location.href).then(function() {
        const btn = event.target.closest('button');
        const originalText = btn.innerHTML;

        btn.innerHTML = '<i class="fas fa-check me-1"></i>คัดลอกแล้ว';
        btn.classList.remove('btn-outline-secondary');
        btn.classList.add('btn-success');

        setTimeout(function() {
            btn.innerHTML = originalText;
            btn.classList.remove('btn-success');
            btn.classList.add('btn-outline-secondary');
        }, 2000);
    }).catch(function(err) {
        console.error('Failed to copy: ', err);
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = window.location.href;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);

        const btn = event.target.closest('button');
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-check me-1"></i>คัดลอกแล้ว';
        btn.classList.remove('btn-outline-secondary');
        btn.classList.add('btn-success');

        setTimeout(function() {
            btn.innerHTML = originalText;
            btn.classList.remove('btn-success');
            btn.classList.add('btn-outline-secondary');
        }, 2000);
    });
}

// Initialize gallery when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Only initialize if there are gallery images
    if (document.querySelectorAll('.gallery-thumbnail').length > 0) {
        window.activityImageGallery = new ActivityImageGallery();
    }

    // Stable modal will auto-initialize from stable-modal.js

    // Add CSS for animations
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        .gallery-thumbnail, .modal-thumb {
            transition: all 0.3s ease;
        }

        .gallery-thumbnail:hover {
            transform: scale(1.05);
        }

        .modal-thumb:hover {
            transform: scale(1.1) !important;
        }

        #mainImage, #modalImage {
            transition: opacity 0.3s ease;
        }

        .modal-thumb.active {
            border-color: #0d6efd !important;
            transform: scale(1.1) !important;
        }
    `;
    document.head.appendChild(style);
});

// Legacy functions for backward compatibility
function changeMainImage(imageSrc, caption) {
    if (window.activityImageGallery) {
        const index = window.activityImageGallery.images.findIndex(img => img.src === imageSrc);
        if (index !== -1) {
            window.activityImageGallery.changeMainImage(index);
        }
    }
}

function changeModalImage(imageSrc, caption) {
    if (window.activityImageGallery) {
        const index = window.activityImageGallery.images.findIndex(img => img.src === imageSrc);
        if (index !== -1) {
            window.activityImageGallery.changeModalImage(index);
        }
    }
}



    // Thumbnail handlers
    document.querySelectorAll('.modal-thumb').forEach((thumb, index) => {
        thumb.addEventListener('click', function(e) {
            e.stopPropagation();
            currentImageIndex = index;
            updateModalImage(index);
        });
    });

    // Keyboard navigation
    document.addEventListener('keydown', function(e) {
        if (modalElement.classList.contains('show')) {
            switch(e.key) {
                case 'Escape':
                    e.preventDefault();
                    forceCloseModal();
                    break;
                case 'ArrowLeft':
                    e.preventDefault();
                    currentImageIndex = (currentImageIndex - 1 + images.length) % images.length;
                    updateModalImage(currentImageIndex);
                    break;
                case 'ArrowRight':
                    e.preventDefault();
                    currentImageIndex = (currentImageIndex + 1) % images.length;
                    updateModalImage(currentImageIndex);
                    break;
            }
        }
    });

    // Backdrop click handler
    modalElement.addEventListener('click', function(e) {
        if (e.target === modalElement) {
            forceCloseModal();
        }
    });

    // Modal event handlers
    modalElement.addEventListener('hidden.bs.modal', function() {
        cleanupModal();
    });

    function updateModalImage(index) {
        if (index < 0 || index >= images.length) return;

        const modalImage = document.getElementById('modalImage');
        if (modalImage && images[index]) {
            modalImage.src = images[index].src;
            modalImage.alt = images[index].alt;
        }

        // Update active thumbnail
        document.querySelectorAll('.modal-thumb').forEach((thumb, i) => {
            if (i === index) {
                thumb.classList.add('active');
            } else {
                thumb.classList.remove('active');
            }
        });
    }

    // Global function to open modal
    window.openImageModal = function(index = 0) {
        currentImageIndex = index;
        updateModalImage(index);

        // Clean up any existing modal
        const existingModal = bootstrap.Modal.getInstance(modalElement);
        if (existingModal) {
            existingModal.dispose();
        }

        // Create new modal
        const modal = new bootstrap.Modal(modalElement, {
            backdrop: true,
            keyboard: true,
            focus: true
        });

        modal.show();
    };
}

function forceCloseModal() {
    const modalElement = document.getElementById('imageModal');
    if (!modalElement) return;

    // Get modal instance and hide
    const modal = bootstrap.Modal.getInstance(modalElement);
    if (modal) {
        modal.hide();
    } else {
        // Force close if no instance
        modalElement.classList.remove('show');
        modalElement.style.display = 'none';
        cleanupModal();
    }
}

function cleanupModal() {
    // Clean up body classes and styles
    document.body.classList.remove('modal-open');
    document.body.style.removeProperty('padding-right');
    document.body.style.removeProperty('overflow');

    // Remove all backdrops
    const backdrops = document.querySelectorAll('.modal-backdrop');
    backdrops.forEach(backdrop => {
        backdrop.remove();
    });

    // Reset modal element
    const modalElement = document.getElementById('imageModal');
    if (modalElement) {
        modalElement.style.display = 'none';
        modalElement.setAttribute('aria-hidden', 'true');
        modalElement.removeAttribute('aria-modal');

        // Dispose modal instance
        const modal = bootstrap.Modal.getInstance(modalElement);
        if (modal) {
            modal.dispose();
        }
    }
}

// Legacy function support (for backward compatibility)
function changeMainImage(imageSrc, caption) {
    if (window.activityImageGallery) {
        const index = window.activityImageGallery.images.findIndex(img => img.src === imageSrc);
        if (index !== -1) {
            window.activityImageGallery.changeMainImage(index);
        }
    }
}

function changeModalImage(imageSrc, caption) {
    if (window.activityImageGallery) {
        const index = window.activityImageGallery.images.findIndex(img => img.src === imageSrc);
        if (index !== -1) {
            window.activityImageGallery.changeModalImage(index);
        }
    }
}


</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\XAMPP\htdocs\PhuyaiPrajakserviceshop\resources\views/frontend/activity-detail.blade.php ENDPATH**/ ?>