<?php $__env->startSection('title', 'แก้ไขแพคเกจ - ระบบจัดการ'); ?>

<?php $__env->startSection('breadcrumb'); ?>
<li class="breadcrumb-item"><a href="<?php echo e(route('admin.packages')); ?>">จัดการแพคเกจ</a></li>
<li class="breadcrumb-item active">แก้ไขแพคเกจ</li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="prevent-excessive-scroll admin-edit-page">
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">แก้ไขแพคเกจ: <?php echo e($package->name); ?></h1>
    <a href="<?php echo e(route('admin.packages')); ?><?php echo e(isset($page) && $page > 1 ? '?page=' . $page : ''); ?>" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i>กลับ
    </a>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-body">
                <form action="<?php echo e(route('admin.packages.update', $package->id)); ?>" method="POST" enctype="multipart/form-data">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('PUT'); ?>
                    <input type="hidden" name="page" value="<?php echo e($page ?? 1); ?>">
                    
                    <div class="mb-3">
                        <label for="name" class="form-label">ชื่อแพคเกจ <span class="text-danger">*</span></label>
                        <input type="text" class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                               id="name" name="name" value="<?php echo e(old('name', $package->name)); ?>" required>
                        <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">คำอธิบาย <span class="text-danger">*</span></label>
                        <textarea class="form-control <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                  id="description" name="description" rows="3" required><?php echo e(old('description', $package->description)); ?></textarea>
                        <?php $__errorArgs = ['description'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    
                    <div class="mb-3">
                        <label for="features" class="form-label">รายการที่รวมในแพคเกจ <span class="text-danger">*</span></label>
                        <textarea class="form-control <?php $__errorArgs = ['features'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                  id="features" name="features" rows="6" required><?php echo e(old('features', $package->features)); ?></textarea>
                        <?php $__errorArgs = ['features'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        <div class="form-text">แต่ละรายการขึ้นบรรทัดใหม่</div>
                    </div>

                    <div class="mb-3">
                        <label for="price_text" class="form-label">ราคา</label>
                        <input type="text" class="form-control <?php $__errorArgs = ['price_text'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                               id="price_text" name="price_text" value="<?php echo e(old('price_text', $package->price_text)); ?>"
                               placeholder="เช่น 50,000 บาท, ตามตกลง, เริ่มต้น 30,000 บาท, สอบถาม">
                        <?php $__errorArgs = ['price_text'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        <div class="form-text">สามารถใส่ตัวเลข ข้อความ หรือทั้งคู่ได้ หากไม่ใส่จะแสดงข้อความ "สอบถามราคา"</div>
                    </div>

                    <div class="mb-3">
                        <label for="duration" class="form-label">ระยะเวลา</label>
                        <input type="text" class="form-control <?php $__errorArgs = ['duration'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                               id="duration" name="duration" value="<?php echo e(old('duration', $package->duration)); ?>"
                               placeholder="เช่น ครั้งเดียว, 1 ปี, 6 เดือน">
                        <?php $__errorArgs = ['duration'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    
                    <div class="mb-3">
                        <label for="sort_order" class="form-label">ลำดับการแสดง</label>
                        <input type="number" class="form-control <?php $__errorArgs = ['sort_order'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                               id="sort_order" name="sort_order" value="<?php echo e(old('sort_order', $package->sort_order)); ?>" min="0">
                        <?php $__errorArgs = ['sort_order'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    
                    <div class="mb-3">
                        <label for="image" class="form-label">รูปภาพ</label>
                        <?php if($package->image): ?>
                        <div class="mb-2">
                            <img src="<?php echo e(asset('storage/' . $package->image)); ?>" alt="<?php echo e($package->name); ?>" 
                                 class="img-thumbnail" style="max-width: 200px;">
                            <div class="form-text">รูปภาพปัจจุบัน</div>
                        </div>
                        <?php endif; ?>
                        <input type="file" class="form-control <?php $__errorArgs = ['image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                               id="image" name="image" accept="image/*">
                        <?php $__errorArgs = ['image'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        <div class="form-text">รองรับไฟล์: JPG, PNG, GIF ขนาดไม่เกิน 2MB (เลือกไฟล์ใหม่หากต้องการเปลี่ยน)</div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_featured" name="is_featured" 
                                           value="1" <?php echo e(old('is_featured', $package->is_featured) ? 'checked' : ''); ?>>
                                    <label class="form-check-label" for="is_featured">
                                        แพคเกจแนะนำ
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                           value="1" <?php echo e(old('is_active', $package->is_active) ? 'checked' : ''); ?>>
                                    <label class="form-check-label" for="is_active">
                                        เปิดใช้งาน
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>บันทึกการแก้ไข
                        </button>
                        <a href="<?php echo e(route('admin.packages')); ?><?php echo e(isset($page) && $page > 1 ? '?page=' . $page : ''); ?>" class="btn btn-secondary">ยกเลิก</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">ข้อมูลแพคเกจ</h5>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>สร้างเมื่อ:</strong></td>
                        <td><?php echo e($package->created_at->format('d/m/Y H:i')); ?></td>
                    </tr>
                    <tr>
                        <td><strong>แก้ไขล่าสุด:</strong></td>
                        <td><?php echo e($package->updated_at->format('d/m/Y H:i')); ?></td>
                    </tr>
                    <tr>
                        <td><strong>สถานะ:</strong></td>
                        <td>
                            <?php if($package->is_active): ?>
                            <span class="badge bg-success">เปิดใช้งาน</span>
                            <?php else: ?>
                            <span class="badge bg-secondary">ปิดใช้งาน</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <td><strong>แนะนำ:</strong></td>
                        <td>
                            <?php if($package->is_featured): ?>
                            <span class="badge bg-warning text-dark">แนะนำ</span>
                            <?php else: ?>
                            <span class="text-muted">ไม่แนะนำ</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">การดำเนินการ</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="<?php echo e(route('packages')); ?>" class="btn btn-outline-primary" target="_blank">
                        <i class="fas fa-eye me-2"></i>ดูในเว็บไซต์
                    </a>
                    <form action="<?php echo e(route('admin.packages.delete', $package->id)); ?>" method="POST"
                          id="deletePackageForm<?php echo e($package->id); ?>">
                        <?php echo csrf_field(); ?>
                        <?php echo method_field('DELETE'); ?>
                        <button type="button" class="btn btn-outline-danger w-100"
                                onclick="handleDeletePackage(<?php echo e($package->id); ?>)">
                            <i class="fas fa-trash me-2"></i>ลบแพคเกจ
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startSection('scripts'); ?>
<script>
// Delete package function with custom modal
async function handleDeletePackage(packageId) {
    const confirmed = await confirmDelete(
        'คุณแน่ใจหรือไม่ที่จะลบแพคเกจนี้? การกระทำนี้ไม่สามารถยกเลิกได้',
        'ยืนยันการลบแพคเกจ'
    );

    if (confirmed) {
        document.getElementById(`deletePackageForm${packageId}`).submit();
    }
}
</script>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\XAMPP\htdocs\PhuyaiPrajakserviceshop\resources\views/admin/packages/edit.blade.php ENDPATH**/ ?>