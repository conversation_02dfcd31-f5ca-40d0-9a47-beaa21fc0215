<?php $__env->startSection('title', 'ผลงาน - ' . ($settings['site_name'] ?? 'บริการจัดงานศพ')); ?>

<?php $__env->startSection('content'); ?>
<!-- Hero Section with Banner Slider -->
<section class="hero-section position-relative <?php echo e($banners->count() === 0 ? 'hero-fallback' : ''); ?>">
    <?php if($banners->count() > 0): ?>
        <!-- Banner Slider -->
        <div id="bannerCarousel" class="carousel slide" data-bs-ride="carousel" data-bs-interval="4000" data-bs-pause="hover">
            <div class="carousel-inner">
                <?php $__currentLoopData = $banners; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $banner): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="carousel-item <?php echo e($index === 0 ? 'active' : ''); ?>">
                    <div class="banner-slide" style="background-image: url('<?php echo e(asset('storage/' . $banner->image_path)); ?>');">
                        <div class="banner-overlay"></div>
                    </div>
                </div>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>

            <?php if($banners->count() > 1): ?>
            <!-- Carousel Controls -->
            <button class="carousel-control-prev" type="button" data-bs-target="#bannerCarousel" data-bs-slide="prev">
                <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                <span class="visually-hidden">Previous</span>
            </button>
            <button class="carousel-control-next" type="button" data-bs-target="#bannerCarousel" data-bs-slide="next">
                <span class="carousel-control-next-icon" aria-hidden="true"></span>
                <span class="visually-hidden">Next</span>
            </button>

            <!-- Carousel Indicators -->
            <div class="carousel-indicators">
                <?php $__currentLoopData = $banners; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $banner): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <button type="button" data-bs-target="#bannerCarousel" data-bs-slide-to="<?php echo e($index); ?>"
                        class="<?php echo e($index === 0 ? 'active' : ''); ?>" aria-current="true" aria-label="Slide <?php echo e($index + 1); ?>"></button>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </div>
            <?php endif; ?>
        </div>

        <!-- Hero Content Overlay สำหรับแบนเนอร์ -->
        <div class="hero-content-overlay">
            <div class="container">
                <div class="text-center">
                    <h1 class="display-4 fw-bold mb-4 text-white">ผลงานการให้บริการ</h1>
                    <p class="lead text-white">ภาพบรรยากาศการให้บริการจัดงานศพที่ผ่านมา</p>
                    <?php if($activities->total() > 0): ?>
                    <div class="mt-4">
                        <span class="badge bg-primary fs-6 px-3 py-2">
                            <i class="fas fa-images me-2"></i>
                            มีผลงานทั้งหมด <?php echo e($activities->total()); ?> รายการ
                        </span>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    <?php else: ?>
        <!-- Hero Content สำหรับกรณีไม่มีแบนเนอร์ -->
        <div class="hero-content-overlay">
            <div class="container">
                <div class="text-center">
                    <h1 class="display-4 fw-bold mb-4">ผลงานการให้บริการ</h1>
                    <p class="lead">ภาพบรรยากาศการให้บริการจัดงานศพที่ผ่านมา</p>
                    <?php if($activities->total() > 0): ?>
                    <div class="mt-4">
                        <span class="badge bg-primary fs-6 px-3 py-2">
                            <i class="fas fa-images me-2"></i>
                            มีผลงานทั้งหมด <?php echo e($activities->total()); ?> รายการ
                        </span>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    <?php endif; ?>
</section>

<!-- Activities Section -->
<section class="py-5">
    <div class="container">
        <?php if($activities->count() > 0): ?>
        <div class="row g-4">
            <?php $__currentLoopData = $activities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $activity): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="col-md-6 col-lg-4">
                <div class="card service-card h-100 activity-card" style="cursor: pointer;" onclick="window.location.href='<?php echo e(route('activities.show', $activity->id)); ?>'">
                    <!-- Cover Image with Overlay -->
                    <div class="card-image-container img-size-large position-relative">
                        <?php
                            $coverImage = $activity->images->where('is_cover', true)->first() ?? $activity->images->first();
                            $coverImagePath = $coverImage ? $coverImage->image_path : $activity->image;
                        ?>
                        <?php if($coverImagePath && file_exists(storage_path('app/public/' . $coverImagePath))): ?>
                        <img src="<?php echo e(asset('storage/' . $coverImagePath)); ?>"
                             class="img-fit-contain activity-image"
                             alt="<?php echo e($activity->title); ?>"
                             style="cursor: pointer;"
                             onclick="window.location.href='<?php echo e(route('activities.show', $activity->id)); ?>'">
                        <?php else: ?>
                        <img src="<?php echo e(asset('images/placeholder.svg')); ?>"
                             class="img-fit-contain activity-image"
                             alt="ไม่มีรูปภาพ"
                             style="cursor: pointer;"
                             onclick="window.location.href='<?php echo e(route('activities.show', $activity->id)); ?>'">
                        <?php endif; ?>

                        <!-- Gallery indicator -->
                        <?php if($activity->images->count() > 1): ?>
                        <div class="position-absolute top-0 end-0 m-2">
                            <span class="badge bg-dark bg-opacity-75">
                                <i class="fas fa-images me-1"></i><?php echo e($activity->images->count()); ?>

                            </span>
                        </div>
                        <?php endif; ?>

                        <!-- Date badge -->
                        <div class="position-absolute top-0 start-0 m-2">
                            <span class="badge bg-primary bg-opacity-90">
                                <i class="fas fa-calendar-alt me-1"></i><?php echo e($activity->activity_date->format('d/m/Y')); ?>

                            </span>
                        </div>

                        <!-- Hover overlay -->
                        <div class="card-hover-overlay">
                            <div class="text-center">
                                <i class="fas fa-eye fa-2x text-white mb-2"></i>
                                <p class="text-white mb-0">คลิกเพื่อดูรายละเอียด</p>
                                <?php if($activity->images->count() > 1): ?>
                                <small class="text-white-50">แกลเลอรี่ <?php echo e($activity->images->count()); ?> รูป</small>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>

                    <div class="card-body d-flex flex-column">
                        <h5 class="card-title">
                            <a href="<?php echo e(route('activities.show', $activity->id)); ?>" class="text-decoration-none text-dark">
                                <?php echo e($activity->title); ?>

                            </a>
                        </h5>
                        <p class="card-text flex-grow-1"><?php echo e($activity->description); ?></p>

                        <!-- Activity Details -->
                        <div class="mb-3">
                            <?php if($activity->location): ?>
                            <div class="mb-2">
                                <small class="text-muted">
                                    <i class="fas fa-map-marker-alt me-1"></i>
                                    <?php echo e($activity->location); ?>

                                </small>
                            </div>
                            <?php endif; ?>
                        </div>

                        <?php if($activity->details): ?>
                        <div class="mb-3">
                            <h6 class="text-muted">รายละเอียดเพิ่มเติม:</h6>
                            <p class="small text-muted"><?php echo e(Str::limit($activity->details, 120)); ?></p>
                        </div>
                        <?php endif; ?>

                        <!-- Activity features/highlights -->
                        <?php if($activity->images->count() > 0): ?>
                        <div class="mb-3">
                            <small class="text-success">
                                <i class="fas fa-check-circle me-1"></i>มีแกลเลอรี่รูปภาพ <?php echo e($activity->images->count()); ?> รูป
                            </small>
                        </div>
                        <?php endif; ?>

                        <div class="mt-auto">
                            <div class="d-grid gap-2">
                                <a href="<?php echo e(route('activities.show', $activity->id)); ?>"
                                   class="btn btn-outline-primary btn-hover-effect">
                                    <i class="fas fa-eye me-2"></i>ดูรายละเอียดและแกลเลอรี่
                                </a>
                                <a href="<?php echo e(route('contact')); ?>"
                                   class="btn btn-primary btn-hover-effect">
                                    <i class="fas fa-envelope me-2"></i>ติดต่อสอบถาม
                                </a>
                            </div>
                            <small class="text-muted d-block text-center mt-2">
                                <i class="fas fa-phone me-1"></i>สอบถามข้อมูลเพิ่มเติมได้ที่เจ้าหน้าที่
                            </small>
                        </div>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <!-- Pagination -->
        <?php if($activities->hasPages()): ?>
        <div class="mt-5">
            <?php echo $__env->make('custom.simple-pagination', ['paginator' => $activities], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        </div>
        <?php endif; ?>

        <?php else: ?>
        <div class="text-center py-5">
            <i class="fas fa-images fa-5x text-muted mb-4"></i>
            <h3 class="text-muted">ยังไม่มีผลงาน</h3>
            <p class="text-muted">กรุณาติดตามผลงานการให้บริการของเราในอนาคต</p>
            <a href="<?php echo e(route('contact')); ?>" class="btn btn-primary">ติดต่อเรา</a>
        </div>
        <?php endif; ?>
    </div>
</section>



<!-- Contact CTA Section -->
<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <h2 class="fw-bold mb-4">ต้องการความช่วยเหลือหรือไม่?</h2>
                <p class="lead mb-4">เราพร้อมให้คำปรึกษาและดูแลท่านในช่วงเวลาที่ยากลำบาก ติดต่อเราได้ตลอด 24 ชั่วโมง</p>
                <div class="d-flex justify-content-center gap-3 flex-wrap">
                    <a href="<?php echo e(route('contact')); ?>" class="btn btn-primary btn-lg">
                        <i class="fas fa-envelope me-2"></i>ติดต่อเรา
                    </a>
                    <a href="tel:<?php echo e($settings['contact_phone'] ?? ''); ?>" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-phone me-2"></i><?php echo e($settings['contact_phone'] ?? '02-xxx-xxxx'); ?>

                    </a>
                </div>
                <div class="mt-4">
                    <small class="text-muted">
                        <i class="fas fa-clock me-1"></i>
                        บริการตลอด 24 ชั่วโมง ทุกวัน
                    </small>
                </div>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<style>
/* Activity Card Enhancements */
.activity-card {
    transition: all 0.3s ease;
    border: none;
    overflow: hidden;
    cursor: pointer;
}

.activity-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15) !important;
}

.activity-image {
    transition: transform 0.3s ease;
}

.activity-card:hover .activity-image {
    transform: scale(1.05);
}

.card-hover-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.activity-card:hover .card-hover-overlay {
    opacity: 1;
}

.btn-hover-effect {
    transition: all 0.3s ease;
}

.btn-hover-effect:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.card-title a:hover {
    color: #2c3e50 !important;
}

/* Gallery badge animation */
.badge {
    transition: all 0.3s ease;
}

.activity-card:hover .badge {
    transform: scale(1.1);
}

/* Loading animation for images */
.activity-image {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

.activity-image[src] {
    background: none;
    animation: none;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Date badge styling */
.badge.bg-primary {
    background: linear-gradient(45deg, #3498db, #2980b9) !important;
}

.badge.bg-dark {
    background: linear-gradient(45deg, #2c3e50, #34495e) !important;
}

/* Responsive improvements */
@media (max-width: 768px) {
    .activity-card:hover {
        transform: none;
    }

    .card-hover-overlay {
        display: none;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add loading state to images
    const images = document.querySelectorAll('.activity-image');

    images.forEach(img => {
        img.addEventListener('load', function() {
            this.classList.add('loaded');
        });

        img.addEventListener('error', function() {
            this.src = '<?php echo e(asset("images/placeholder.svg")); ?>';
            this.alt = 'ไม่สามารถโหลดรูปภาพได้';
        });
    });

    // Activity card click functionality - handled by onclick attribute
    // Prevent button clicks from triggering card navigation
    const buttons = document.querySelectorAll('.activity-card .btn');
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.stopPropagation();
        });
    });

    // Smooth scroll for pagination
    const paginationLinks = document.querySelectorAll('.pagination a');
    paginationLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            // Smooth scroll to top of activities section
            setTimeout(() => {
                document.querySelector('.hero-section').scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }, 100);
        });
    });

    // Add stagger animation for cards
    const cards = document.querySelectorAll('.activity-card');
    cards.forEach((card, index) => {
        card.style.animationDelay = `${index * 0.1}s`;
        card.classList.add('fade-in-up');
    });
});

// Add CSS animation for cards
const style = document.createElement('style');
style.textContent = `
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .fade-in-up {
        animation: fadeInUp 0.6s ease forwards;
        opacity: 0;
    }
`;
document.head.appendChild(style);
</script>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Activity card click functionality
    document.querySelectorAll('.activity-card').forEach(function(card) {
        card.addEventListener('click', function(e) {
            // Don't navigate if clicking on the button
            if (e.target.closest('.btn')) {
                return;
            }

            const link = card.querySelector('a[href*="activities"]');
            if (link) {
                window.location.href = link.href;
            }
        });
    });
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\XAMPP\htdocs\PhuyaiPrajakserviceshop\resources\views/frontend/activities.blade.php ENDPATH**/ ?>